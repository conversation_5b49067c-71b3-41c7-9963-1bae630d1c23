package com.amobilab.ezmath.ai.utils

import amobi.module.common.utils.FirebaseAssist
import amobi.module.common.utils.dlog
import android.app.Activity
import android.os.Bundle
import android.widget.Button
import android.widget.LinearLayout
import android.widget.TextView
import com.amobilab.ezmath.ai.R

/**
 * Activity để test tracking events cho home button và recent button
 */
class TrackingTestActivity : Activity() {
    
    private var isUserLeavingByHomeButton = false
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // Tạo UI đơn giản để test
        val layout = LinearLayout(this).apply {
            orientation = LinearLayout.VERTICAL
            setPadding(50, 50, 50, 50)
        }
        
        val titleText = TextView(this).apply {
            text = "Tracking Test Activity"
            textSize = 20f
            setPadding(0, 0, 0, 30)
        }
        
        val instructionText = TextView(this).apply {
            text = "Nhấn Home button hoặc Recent button để test tracking events. <PERSON><PERSON><PERSON> tra log để xem kết quả."
            setPadding(0, 0, 0, 30)
        }
        
        val setScreenButton = Button(this).apply {
            text = "Set Screen: HomeScreen_SettingTab"
            setOnClickListener {
                ScreenTracker.setCurrentScreen("HomeScreen")
                ScreenTracker.setCurrentTab("SettingTab")
                dlog("Screen set to: ${ScreenTracker.getCurrentScreenForTracking()}")
            }
        }
        
        val setScreen2Button = Button(this).apply {
            text = "Set Screen: CoinHistory"
            setOnClickListener {
                ScreenTracker.setCurrentScreen("CoinHistory")
                dlog("Screen set to: ${ScreenTracker.getCurrentScreenForTracking()}")
            }
        }
        
        layout.addView(titleText)
        layout.addView(instructionText)
        layout.addView(setScreenButton)
        layout.addView(setScreen2Button)
        
        setContentView(layout)
        
        // Set default screen
        ScreenTracker.setCurrentScreen("HomeScreen")
        ScreenTracker.setCurrentTab("SettingTab")
    }
    
    override fun onUserLeaveHint() {
        super.onUserLeaveHint()
        isUserLeavingByHomeButton = true
        
        // Log home button pressed event
        val currentScreen = ScreenTracker.getCurrentScreenForTracking()
        FirebaseAssist.Companion.instance.logHomeButtonPressed(currentScreen)
        
        dlog("HOME BUTTON PRESSED - Screen: $currentScreen")
    }
    
    override fun onStop() {
        super.onStop()

        // Nếu không phải do home button và activity không bị finish thì có thể là recent button
        if (!isUserLeavingByHomeButton && !isFinishing) {
            val currentScreen = ScreenTracker.getCurrentScreenForTracking()
            FirebaseAssist.Companion.instance.logRecentButtonPressed(currentScreen)

            dlog("RECENT BUTTON PRESSED - Screen: $currentScreen")
        }
    }
    
    override fun onResume() {
        super.onResume()
        isUserLeavingByHomeButton = false
    }
}
