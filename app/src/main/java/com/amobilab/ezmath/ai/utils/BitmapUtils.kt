package com.amobilab.ezmath.ai.utils

import amobi.module.common.configs.CommFigs
import android.content.ContentResolver
import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.os.Build
import androidx.compose.ui.graphics.ImageBitmap
import androidx.compose.ui.graphics.asImageBitmap
import androidx.core.graphics.scale
import androidx.core.net.toUri
import java.io.ByteArrayOutputStream
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.io.InputStream
import kotlin.math.max

object BitmapUtils {

    fun getBitmapFromByteArray(input: ByteArray?): Bitmap {
        return BitmapFactory.decodeByteArray(input, 0, input!!.size)
    }

    fun getBitmapFromUriOrFile(contentResolver: ContentResolver, uriString: String): ImageBitmap? {
        return try {
            val uri = uriString.toUri()

            if (uriString.startsWith("file:/")) {
                // Xử lý đường dẫn tệp
                val file = File(uriString.replace("file:/", ""))
                val bitmap = BitmapFactory.decodeFile(file.absolutePath)
                bitmap.asImageBitmap()
            } else {
                // Xử lý URI nội dung
                val inputStream: InputStream? = contentResolver.openInputStream(uri)
                val bitmap = BitmapFactory.decodeStream(inputStream)
                bitmap?.asImageBitmap()
            }
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }

    fun resizeImageBitmapToStandardSize(bitmap: Bitmap): Bitmap {
        val targetWidth = if (!CommFigs.IS_WEAK_DEVICE) 1080 else 1080 / 2
        val targetHeight = if (!CommFigs.IS_WEAK_DEVICE) 720 else 720 / 2

        // Kiểm tra kích thước bitmap gốc
        if (bitmap.width > targetWidth || bitmap.height > targetHeight) {
            val scaleWidth = targetWidth.toFloat() / bitmap.width
            val scaleHeight = targetHeight.toFloat() / bitmap.height
            val scaleFinal = max(scaleWidth, scaleHeight)
            return bitmap.scale((bitmap.width * scaleFinal).toInt(), (bitmap.height * scaleFinal).toInt(), !CommFigs.IS_WEAK_DEVICE)
        }
        return bitmap
    }

    fun saveBitmapToCache(context: Context, bitmap: Bitmap, fileName: String): String {
        val cacheDir = context.cacheDir
        val file = File(cacheDir, fileName)

        return try {
            FileOutputStream(file).use { outputStream ->
                bitmap.compress(Bitmap.CompressFormat.JPEG, 100, outputStream)
            }
            // Trả về đường dẫn URL của file
            file.toURI().toString()
        } catch (e: IOException) {
            e.printStackTrace()
            ""
        }
    }

    /**
     * Scales the given bitmap to fit within [maxWidth] and [maxHeight], then compresses it to a JPEG byte array.
     *
     * @param bitmap The original bitmap to scale and compress.
     * @param maxWidth The maximum width of the scaled bitmap (default: 800).
     * @param maxHeight The maximum height of the scaled bitmap (default: 800).
     * @param quality The JPEG compression quality (0-100, default: 80).
     * @return The compressed byte array of the scaled bitmap.
     */
    fun getScaledByteArrayFromBitmap(
        bitmap: Bitmap,
        maxWidth: Int = 512,
        maxHeight: Int = 512,
        quality: Int = 80
    ): ByteArray {
        val width = bitmap.width
        val height = bitmap.height
        val scale = minOf(maxWidth.toFloat() / width, maxHeight.toFloat() / height, 1.0f)
        val scaledWidth = (width * scale).toInt()
        val scaledHeight = (height * scale).toInt()

        val scaledBitmap = if (scale < 1.0f) {
            bitmap.scale(scaledWidth, scaledHeight)
        } else {
            bitmap
        }

        val stream = ByteArrayOutputStream()
        scaledBitmap.compress(Bitmap.CompressFormat.WEBP, quality, stream)
        return stream.toByteArray()
    }

    fun getScaledByteArrayFromBitmap2(
        bitmap: Bitmap,
        maxWidth: Int = 256, // Giảm kích thước tối đa
        maxHeight: Int = 256,
        quality: Int = 60 // Giảm chất lượng nén
    ): ByteArray {
        // Tính tỷ lệ scale
        val scale = minOf(maxWidth.toFloat() / bitmap.width, maxHeight.toFloat() / bitmap.height, 1.0f)
        val scaledWidth = (bitmap.width * scale).toInt().coerceAtLeast(1)
        val scaledHeight = (bitmap.height * scale).toInt().coerceAtLeast(1)

        // Scale bitmap nếu cần
        val scaledBitmap = if (scale < 1.0f) {
            bitmap.scale(scaledWidth, scaledHeight)
        } else {
            bitmap
        }

        // Nén với WEBP_LOSSY
        return ByteArrayOutputStream().use { stream ->
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                scaledBitmap.compress(Bitmap.CompressFormat.WEBP_LOSSY, quality, stream)
            }
            val result = stream.toByteArray()
            // Kiểm tra kích thước file (byte)
            if (result.size > 1024 * 1024) { // Giới hạn 1MB
                // Tái nén với chất lượng thấp hơn nếu quá lớn
                stream.reset()
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                    scaledBitmap.compress(Bitmap.CompressFormat.WEBP_LOSSY, quality - 20, stream)
                }
            }
            stream.toByteArray()
        }
    }

}