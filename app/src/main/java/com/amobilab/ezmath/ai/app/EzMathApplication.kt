/////////////////////////////////////////////////////////
//                                                     //
//                       _oo0oo_                       //
//                      o8888888o                      //
//                      88" . "88                      //
//                      (| -_- |)                      //
//                      0\  =  /0                      //
//                    ___/`---'\___                    //
//                  .' \\|     |// '.                  //
//                 / \\|||  :  |||// \                 //
//                / _||||| -:- |||||- \                //
//               |   | \\\  -  /// |   |               //
//               | \_|  ''\---/''  |_/ |               //
//               \  .-\__  '-'  ___/-. /               //
//             ___'. .'  /--.--\  `. .'___             //
//          ."" '<  `.___\_<|>_/___.' >' "".           //
//         | | :  `- \`.;`\ _ /`;.`/ - ` : | |         //
//         \  \ `_.   \_ __\ /__ _/   .-` /  /         //
//     =====`-.____`.___ \_____/___.-`___.-'=====      //
//                       `=---='                       //
//                                                     //
//                                                     //
//     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~     //
//                                                     //
//  KHÔNG BUG!     KHÔNG CRASH!   RELEASE THÀNH CÔNG!  //
//                                                     //
//                    A DI ĐÀ PHẬT!                    //
//                                                     //
/////////////////////////////////////////////////////////

package com.amobilab.ezmath.ai.app

import amobi.module.common.CommApplication
import amobi.module.common.advertisements.AdvertsConfig
import amobi.module.common.advertisements.AdvertsInstance
import amobi.module.common.configs.CommFigs
import amobi.module.common.configs.PrefAssist
import amobi.module.common.configs.RconfAssist
import amobi.module.common.utils.FirebaseAssist
import amobi.module.common.utils.MixedUtils
import amobi.module.common.utils.debugLog
import android.content.Context
import androidx.lifecycle.viewModelScope
import androidx.multidex.MultiDex
import com.amobilab.ezmath.ai.BuildConfig
import com.amobilab.ezmath.ai.R
import com.amobilab.ezmath.ai.data.db.AppDatabase
import com.amobilab.ezmath.ai.data.pref.PrefConst
import com.amobilab.ezmath.ai.data.pref.RconfConst
import com.amobilab.ezmath.ai.presentation.common.shared_values.ModelAiMode
import com.amobilab.ezmath.ai.utils.CustomAppCheckProviderFactory
import com.amobilab.ezmath.ai.utils.ScreenTracker
import com.google.firebase.appcheck.FirebaseAppCheck
import com.google.firebase.appcheck.playintegrity.PlayIntegrityAppCheckProviderFactory
import dagger.hilt.android.HiltAndroidApp
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch

@HiltAndroidApp
class EzMathApplication : CommApplication() {

    private val appScope = CoroutineScope(Dispatchers.IO)

    override fun attachBaseContext(base: Context) {
        super.attachBaseContext(base)
        MultiDex.install(this)
    }

    override fun onCreate() {
        super.onCreate()

        CommFigs.setupBuildConfigs(BuildConfig.DEBUG, BuildConfig.FLAVOR)

        FirebaseAppCheck.getInstance().installAppCheckProviderFactory(
            if (CommFigs.IS_PRODUCT && !CommFigs.IS_DEBUG)
                PlayIntegrityAppCheckProviderFactory.getInstance()
            else
                CustomAppCheckProviderFactory.getInstance(),
        )

        PrefAssist.defaultValueString = PrefConst::getDefString
        PrefAssist.defaultValueBoolean = PrefConst::getDefBoolean
        PrefAssist.defaultValueInt = PrefConst::getDefInt
        PrefAssist.defaultValueLong = PrefConst::getDefLong

        val appDb = AppDatabase.getInstance()
        appDb.init(this.applicationContext)
        appScope.launch {
            appDb.migrateFromRoom()
            appDb.connectToBackend()
        }

        appInitialize()
    }

    override fun appInitialize(): Boolean {
        if (!super.appInitialize())
            return false

        RconfAssist.setTestBoolean(RconfConst.IS_USE_GATEWAY_API, true)
        if (CommFigs.IS_SHOW_TEST_OPTION) {
            AdvertsConfig.instance.isHideAd = true
        }

        FirebaseAssist.Companion.instance.fetchRemoteConfig(
            R.xml.remote_config_defaults,
            onSuccessFetch = {
                if (CommFigs.IS_SHOW_TEST_OPTION) {
                    MixedUtils.showToast(RconfConst.getAbTestVersion())
                }
            },
            onCompleteFetch = {
                if (RconfAssist.getInt(RconfConst.SHOW_AI_TYPE) == RconfConst.SHOW_AI_GEMINI_TYPE) {
                    PrefAssist.setString(PrefConst.MODEL_AI, ModelAiMode.GEMINI.name)
                } else if (RconfAssist.getInt(RconfConst.SHOW_AI_TYPE) == RconfConst.SHOW_AI_GPT_TYPE) {
                    PrefAssist.setString(PrefConst.MODEL_AI, ModelAiMode.GPT.name)
                }
            }
        )

        AdvertsInstance.Companion.clearLastTimeShowedFullAd()

        return true
    }

    /**
     * Override để detect khi app bị minimize bằng recent button
     */
    override fun onStop(owner: LifecycleOwner) {
        super.onStop(owner)

        // Khi app bị stop, nếu không phải do home button thì có thể là recent button
        if (!ScreenTracker.isUserLeavingByHomeButton) {
            val currentScreen = ScreenTracker.getCurrentScreenForTracking()
            FirebaseAssist.Companion.instance.logRecentButtonPressed(currentScreen)

            if (CommFigs.IS_DEBUG) {
                debugLog("Recent button pressed (App level) - Screen: $currentScreen")
            }
        }

        // Reset flag sau khi log
        ScreenTracker.setUserLeavingByHomeButton(false)
    }
}