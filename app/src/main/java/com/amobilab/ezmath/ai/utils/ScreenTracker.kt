package com.amobilab.ezmath.ai.utils

import com.amobilab.ezmath.ai.presentation.navigation.ScreenRoutes

/**
 * Singleton class để track screen hiện tại và tab hiện tại
 * Sử dụng để log tracking events khi user tắt app
 */
object ScreenTracker {
    
    private var currentScreenName: String = ""
    private var currentTabName: String = ""
    
    /**
     * Set screen hiện tại
     */
    fun setCurrentScreen(screenName: String) {
        currentScreenName = screenName
        // Reset tab name khi chuyển screen
        if (screenName != "HomeScreen") {
            currentTabName = ""
        }
    }
    
    /**
     * Set tab hiện tại (chỉ dành cho HomeScreen)
     */
    fun setCurrentTab(tabRoute: String) {
        if (currentScreenName == "HomeScreen") {
            currentTabName = when (tabRoute) {
                ScreenRoutes.AiChatTab().route -> "AiChatTab"
                ScreenRoutes.ScanTab().route -> "ScanTab"
                ScreenRoutes.HistoryTab().route -> "HistoryTab"
                ScreenRoutes.SettingTab().route -> "SettingTab"
                else -> ""
            }
        }
    }
    
    /**
     * Lấy screen name để log tracking event
     * Nếu là HomeScreen thì sẽ kèm theo tab name
     */
    fun getCurrentScreenForTracking(): String {
        return when {
            currentScreenName == "HomeScreen" && currentTabName.isNotEmpty() -> {
                "HomeScreen_$currentTabName"
            }
            currentScreenName == "CoinHistory" -> "CoinHistory"
            currentScreenName.isNotEmpty() -> currentScreenName
            else -> "Unknown"
        }
    }
    
    /**
     * Set screen name từ route
     */
    fun setScreenFromRoute(route: String?) {
        val screenName = when {
            route?.contains("HomeScreen") == true -> "HomeScreen"
            route?.contains("CoinHistory") == true -> "CoinHistory"
            route?.contains("OnboardingScreen") == true -> "OnboardingScreen"
            route?.contains("ChatScreen") == true -> "ChatScreen"
            route?.contains("ScanDetail") == true -> "ScanDetail"
            route?.contains("Calculator") == true -> "Calculator"
            route?.contains("InAppPurchaseRoute") == true -> "InAppPurchaseRoute"
            route?.contains("SignIn") == true -> "SignIn"
            route?.contains("UserDeleteScreen") == true -> "UserDeleteScreen"
            route?.contains("SetTheme") == true -> "SetTheme"
            route?.contains("FeedbackSuggestion") == true -> "FeedbackSuggestion"
            route?.contains("Literature") == true -> "Literature"
            route?.contains("ResearchAndAnalysis") == true -> "ResearchAndAnalysis"
            route?.contains("WriteAnEssay") == true -> "WriteAnEssay"
            else -> "Unknown"
        }
        setCurrentScreen(screenName)
    }
}
