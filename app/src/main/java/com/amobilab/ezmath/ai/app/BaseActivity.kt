package com.amobilab.ezmath.ai.app

import amobi.module.common.CommApplication
import amobi.module.common.configs.CommFigs
import amobi.module.common.configs.PrefAssist.initPreviewPreferences
import amobi.module.common.configs.RconfAssist
import amobi.module.common.utils.FirebaseAssist
import amobi.module.common.utils.dlog
import amobi.module.common.views.CommActivity
import amobi.module.compose.theme.AppThemeWrapper
import android.content.pm.ActivityInfo
import android.content.res.Configuration
import android.os.Bundle
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.runtime.Composable
import androidx.compose.runtime.livedata.observeAsState
import com.amobilab.ezmath.ai.data.pref.RconfConst
import com.amobilab.ezmath.ai.presentation.common.shared_values.AppThemeMode
import com.amobilab.ezmath.ai.utils.ScreenTracker
import javax.inject.Inject

abstract class BaseActivity : CommActivity() {
    @Inject
    lateinit var coinViewModel: CoinViewModel

    // Biến để track xem có phải user nhấn home button không
    private var isUserLeavingByHomeButton = false

    // Biến để track xem có đang trong quá trình navigation internal không
    private var isInternalNavigation = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        if (CommFigs.IS_DEBUG)
            dlog("Compact Screen: ${isScreenCompact()}")

        requestedOrientation = if (isScreenCompact())
            ActivityInfo.SCREEN_ORIENTATION_PORTRAIT else
            ActivityInfo.SCREEN_ORIENTATION_FULL_USER

        initPreviewPreferences(this)
        setContent {
            val appThemeMode = coinViewModel.appThemeMode.observeAsState()

            AppThemeWrapper(
                darkTheme = when (appThemeMode.value) {
                    AppThemeMode.DARK -> true
                    AppThemeMode.LIGHT -> false
                    AppThemeMode.SYSTEM, null -> {
                        val nightModeFlags = CommApplication.appContext.resources
                            .configuration.uiMode and Configuration.UI_MODE_NIGHT_MASK
                        nightModeFlags == Configuration.UI_MODE_NIGHT_YES
                    }

                }
            ) {
                MainContentCompose()
            }
        }


        enableEdgeToEdge()

        if (RconfAssist.getBoolean(RconfConst.IS_HIDE_SYSTEM_NAV_BAR))
            hideNavigationBarUI()
    }

    /**
     * Được gọi khi user nhấn home button
     * Đây là cách chính để detect home button press
     */
    override fun onUserLeaveHint() {
        super.onUserLeaveHint()
        isUserLeavingByHomeButton = true

        // Log home button pressed event
        val currentScreen = ScreenTracker.getCurrentScreenForTracking()
        FirebaseAssist.Companion.instance.logHomeButtonPressed(currentScreen)

        if (CommFigs.IS_DEBUG) {
            dlog("Home button pressed - Screen: $currentScreen")
        }
    }

    /**
     * Được gọi khi activity bị pause
     * Nếu không phải do home button thì có thể là recent button
     */
    override fun onPause() {
        super.onPause()

        // Nếu không phải do home button thì có thể là recent button
        if (!isUserLeavingByHomeButton) {
            val currentScreen = ScreenTracker.getCurrentScreenForTracking()
            FirebaseAssist.Companion.instance.logRecentButtonPressed(currentScreen)

            if (CommFigs.IS_DEBUG) {
                dlog("Recent button pressed - Screen: $currentScreen")
            }
        }
    }

    /**
     * Reset flag khi activity resume
     */
    override fun onResume() {
        super.onResume()
        isUserLeavingByHomeButton = false
    }

    @Composable
    protected abstract fun MainContentCompose()

    private fun setWindowFlag(bits: Int, on: Boolean) {
        val winParams = window.attributes
        if (on) {
            winParams.flags = winParams.flags or bits
        } else {
            winParams.flags = winParams.flags and bits.inv()
        }
        window.attributes = winParams
    }
}