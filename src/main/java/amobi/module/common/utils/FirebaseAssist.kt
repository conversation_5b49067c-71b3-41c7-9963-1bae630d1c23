package amobi.module.common.utils

import amobi.module.common.CommApplication
import amobi.module.common.advertisements.AdvertsConfig
import amobi.module.common.configs.CommFigs
import amobi.module.common.configs.PrefAssist
import amobi.module.common.configs.PrefComm
import amobi.module.common.configs.RconfAssist
import amobi.module.common.configs.RconfComm
import android.content.pm.PackageManager
import android.os.Build
import android.os.Bundle
import android.util.Log
import android.view.View
import com.google.android.gms.tasks.Task
import com.google.firebase.FirebaseApp
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.google.firebase.ktx.Firebase
import com.google.firebase.remoteconfig.FirebaseRemoteConfig
import com.google.firebase.remoteconfig.FirebaseRemoteConfigSettings
import com.google.firebase.remoteconfig.ktx.remoteConfig
import org.joda.time.LocalTime
import java.util.Arrays
import java.util.Calendar
import java.util.Locale
import kotlin.math.floor

class FirebaseAssist private constructor() {
    companion object {
        private var shared: FirebaseAssist? = null
        var isRCFetchCompleted = false
        val instance: FirebaseAssist
            get() {
                if (shared == null) {
                    shared = FirebaseAssist()
                }
                return shared!!
            }
        const val AD_BANNER = 1
        const val AD_INTERSTITIAL = 2
        const val AD_VIDEO = 3
        const val AD_NATIVE = 4
        const val AD_OPEN_AD = 5
        const val AD_REWARD = 6
        const val AD_LOAD = 1
        const val AD_RELOAD = 2
        const val AD_LOADED = 3
        const val AD_LOAD_FAILED = 4
        const val AD_SHOW_FAILED = 1
        const val AD_SHOW_SUCCESS = 2
    }

    private var mFirebaseAnalytics: FirebaseAnalytics? = null
    private var mFirebaseRemoteConfig: FirebaseRemoteConfig? = null

    var isAnalyticsEnabled = true
        private set
    var isAnalyticsAdsEnabled = true
        private set
    var isAnalyticsAdsBannerEnabled = true
        private set
    var isAnalyticsAdsInterEnabled = true
        private set
    var isAnalyticsAdsVideoEnabled = true
        private set
    var isAnalyticsAdsNativeEnabled = true
        private set
    var isAnalyticsAdsOpenAdEnabled = true
        private set

    var isAnalyticsIapEnabled = true
        private set
    var isAnalyticsOpenAppEnabled = true
        private set
    var isAnalyticsClickButtonEnabled = true
        private set
    var isAnalyticsPaidAdImpressionEnabled = true
        private set
    var isAnalyticsScreenViewEnabled = true
        private set
    var isAnalyticsRateMeEnabled = true
        private set
    var isUserPropertiesEnabled = true
        private set

    fun setContextFireBase() {
        val context = CommApplication.appContext
        FirebaseApp.initializeApp(context)
        mFirebaseAnalytics = FirebaseAnalytics.getInstance(context)
        mFirebaseAnalytics?.setAnalyticsCollectionEnabled(true)
        FirebaseCrashlytics.getInstance().isCrashlyticsCollectionEnabled = true
    }

    fun fetchRemoteConfig(
        xmlRes: Int,
        onSuccessFetch: (() -> Unit)? = null,
        onCompleteFetch: (() -> Unit)? = null,
    ) {
        val remoteConfig = mFirebaseRemoteConfig ?: Firebase.remoteConfig
        if (mFirebaseRemoteConfig == null) {
            mFirebaseRemoteConfig = remoteConfig
            val configSettings =
                FirebaseRemoteConfigSettings
                    .Builder()
                    .setMinimumFetchIntervalInSeconds(
                        if (CommFigs.IS_SHOW_TEST_OPTION)
                            0L
                        else
                            2 * CommFigs.SECONDS_HOUR,
                    ).build()
            remoteConfig.setConfigSettingsAsync(configSettings)
            remoteConfig.setDefaultsAsync(xmlRes)
        }

        if (PrefAssist.getBoolean(PrefComm.FETCH_RC_ONCE, false)) {
            remoteConfig.activate().addOnCompleteListener { task: Task<Boolean> ->
                onRCActivateComplete(onSuccessFetch)
                if (!task.isComplete) return@addOnCompleteListener
                isRCFetchCompleted = true
                onCompleteFetch?.invoke()
                if (System.currentTimeMillis() - PrefAssist.getLong(PrefComm.FETCH_RC_SUCCESS_MILLIS) < CommFigs.MILLIS_HOUR)
                    return@addOnCompleteListener
                remoteConfig.fetch().addOnCompleteListener {
                    if (it.isSuccessful) {
                        PrefAssist.setLong(PrefComm.FETCH_RC_SUCCESS_MILLIS, System.currentTimeMillis())
                        AdvertsConfig.instance.updateEnableAdsBooleans()
                    }
                }
            }
        } else {
            remoteConfig.activate().addOnCompleteListener { _: Task<Boolean> ->
                remoteConfig.fetchAndActivate().addOnCompleteListener { task: Task<Boolean> ->
                    if (task.isSuccessful) {
                        PrefAssist.setLong(PrefComm.FETCH_RC_SUCCESS_MILLIS, System.currentTimeMillis())
                        onRCActivateComplete(onSuccessFetch)
                    } else {
                        Log.e("Firebase fetchRC", "Error: $task")
                    }
                    if (!task.isComplete) return@addOnCompleteListener
                    isRCFetchCompleted = true
                    onCompleteFetch?.invoke()
                }
            }
        }
    }

    private fun onRCActivateComplete(onSuccessFetch: (() -> Unit)? = null) {
        PrefAssist.setBoolean(PrefComm.FETCH_RC_ONCE, true)
        isAnalyticsEnabled = RconfAssist.getBoolean(RconfComm.COMM_IS_ENABLE_ANALYTICS)

        isAnalyticsAdsEnabled = RconfAssist.getBoolean(RconfComm.COMM_IS_ENABLE_ADS_ANALYTICS)
        isAnalyticsAdsBannerEnabled = RconfAssist.getBoolean(RconfComm.COMM_IS_ENABLE_ADS_BANNER_ANALYTICS)
        isAnalyticsAdsInterEnabled = RconfAssist.getBoolean(RconfComm.COMM_IS_ENABLE_ADS_INTER_ANALYTICS)
        isAnalyticsAdsVideoEnabled = RconfAssist.getBoolean(RconfComm.COMM_IS_ENABLE_ADS_VIDEO_ANALYTICS)
        isAnalyticsAdsNativeEnabled = RconfAssist.getBoolean(RconfComm.COMM_IS_ENABLE_ADS_NATIVE_ANALYTICS)
        isAnalyticsAdsOpenAdEnabled = RconfAssist.getBoolean(RconfComm.COMM_IS_ENABLE_ADS_OPEN_APP_ANALYTICS)

        isAnalyticsIapEnabled = RconfAssist.getBoolean(RconfComm.COMM_IS_ENABLE_IAP_ANALYTICS)
        isAnalyticsOpenAppEnabled = RconfAssist.getBoolean(RconfComm.COMM_IS_ENABLE_OPEN_APP_ANALYTICS)
        isAnalyticsClickButtonEnabled = RconfAssist.getBoolean(RconfComm.COMM_IS_ENABLE_CLICK_BUTTON_ANALYTICS)
        isAnalyticsPaidAdImpressionEnabled = RconfAssist.getBoolean(RconfComm.COMM_IS_ENABLE_PAID_AD_IMPRESSION_ANALYTICS)
        isAnalyticsScreenViewEnabled = RconfAssist.getBoolean(RconfComm.COMM_IS_ENABLE_SCREEN_VIEW_ANALYTICS)
        isAnalyticsRateMeEnabled = RconfAssist.getBoolean(RconfComm.COMM_IS_ENABLE_RATE_ME_ANALYTICS)

        isUserPropertiesEnabled = RconfAssist.getBoolean(RconfComm.COMM_IS_ENABLE_USER_PROPERTIES)
        AdvertsConfig.instance.updateEnableAdsBooleans()
        onSuccessFetch?.invoke()
    }

    // ================================================================================================
    fun logCustomEvent(
        eventName: String,
        params: Bundle? = null,
    ) {
        if (!isAnalyticsEnabled) return
        DebugLogCustom.logd("logCustomEvent $eventName")
        mFirebaseAnalytics?.logEvent(eventName, params ?: Bundle())
    }

    fun logCustomEventOnce(
        eventName: String,
        params: Bundle? = null,
    ) {
        if (!isAnalyticsEnabled) return

        val key = "CustomLogEventOncePrefix_$eventName"
        val hasWroteOnce = PrefAssist.getBoolean(key, false)
        if (hasWroteOnce) return

        PrefAssist.setBoolean(key, true)
        mFirebaseAnalytics?.logEvent(eventName, params ?: Bundle())
    }
    // ================================================================================================

    fun logBttnClickedRaw(bttnName: String?) {
        if (!isAnalyticsEnabled || !isAnalyticsClickButtonEnabled) return
        if (bttnName.isNullOrEmpty()) return
        val bundle = Bundle()
        bundle.putString("click_btn_ev_name", bttnName)
        bundle.putLong("click_btn_ev_time", MixedUtils.currentTimeSeconds() - CommApplication.startupTimeInSeconds)
        mFirebaseAnalytics?.logEvent("click_btn_ev", bundle)
    }

    fun logBttnClicked(
        tag: String,
        tagDialog: String? = null,
        evName: String,
    ) {
        if (!isAnalyticsEnabled || !isAnalyticsClickButtonEnabled) return
        val log = StringBuilder(tag + "_")
        if (!tagDialog.isNullOrEmpty()) {
            log.append(tagDialog + "_")
        }
        log.append(evName)
        logBttnClickedRaw(log.toString())
    }

    fun logBttnClicked(
        tagScreen: String,
        tagDialog: String? = null,
        v: View?,
    ) {
        if (!isAnalyticsEnabled || !isAnalyticsClickButtonEnabled) return
        if (v == null) return
        val log = StringBuilder(tagScreen + "_")
        if (!tagDialog.isNullOrEmpty()) {
            log.append(tagDialog + "_")
        }
        try {
            log.append(v.resources.getResourceEntryName(v.id))
        } catch (e: Exception) {
            log.append(v.id)
        }
        logBttnClickedRaw(log.toString())
    }

    var openAppSource = ""

    fun logOpenAppEvent(where: String) {
        if (!isAnalyticsEnabled || !isAnalyticsOpenAppEnabled) return
        val bundle = Bundle()
        openAppSource = where
        bundle.putString("open_app_from_ev_where_time", "OAF_ev_" + where + "_" + LocalTime.now().hourOfDay)
        val isFirstTimeOpen = PrefAssist.getBoolean("open_app_ev_is_first_time", true)
        bundle.putString("open_app_ev_is_first_time", if (isFirstTimeOpen) "1" else "0")
        if (isFirstTimeOpen) PrefAssist.setBoolean("open_app_ev_is_first_time", false)
        mFirebaseAnalytics?.logEvent("open_app_from_ev", bundle)
    }

    fun logScreenView(screenName: String?) {
        if (!isAnalyticsEnabled || !isAnalyticsScreenViewEnabled) return
        val bundle = Bundle()
        bundle.putString("screen_view_ev_name", screenName)
        bundle.putLong("screen_view_ev_delta_t", MixedUtils.currentTimeSeconds() - CommApplication.foregroundTimeInSeconds)
        bundle.putLong("screen_view_ev_time", MixedUtils.currentTimeSeconds() - CommApplication.startupTimeInSeconds)
        mFirebaseAnalytics?.logEvent("screen_view_ev_new", bundle)
        if (isUserPropertiesEnabled)
            mFirebaseAnalytics?.setUserProperty("screen_view", screenName)
    }

    fun logIapEvent(
        type: Int,
        where: String,
        uiType: Int,
        result: Int,
    ) {
        if (!isAnalyticsEnabled || !isAnalyticsIapEnabled) return
        val bundle = Bundle()
        bundle.putString("iap_ev_sub_type", type.toString())
        bundle.putString("iap_ev_where", where)
        bundle.putString("iap_ev_ui_type", uiType.toString())
        bundle.putString("iap_ev_result", result.toString())
        mFirebaseAnalytics?.logEvent("iap_ev", bundle)
    }

    fun logRateBttnClicked(
        where: String,
        action: String,
    ) {
        if (!isAnalyticsEnabled || !isAnalyticsRateMeEnabled) return
        val bundle = Bundle()
        bundle.putString("show_rate_dialog_ev_where_action", "RateDlg_ev_" + where + "_" + action)
        mFirebaseAnalytics?.logEvent("show_rate_dialog_ev", bundle)
    }

    fun logTimeOpenAppEv() {
        if (!isAnalyticsEnabled || !isAnalyticsOpenAppEnabled) return
        val lastMinutesOpenedApp = PrefAssist.getLong(PrefComm.LAST_MINUTES_OPENED_APP, 0)
        val lastHourOpenedApp = PrefAssist.getInt(PrefComm.LAST_HOURS_OPENED_APP, -1)

        val bundle = Bundle()
        bundle.putString("time_open_app_ev_current_in_hour", LocalTime.now().hourOfDay.toString())
        bundle.putString("time_open_app_ev_last_in_hour", lastHourOpenedApp.toString())
        bundle.putString("time_open_app_ev_delta", (MixedUtils.currentTimeMinutes() - lastMinutesOpenedApp).toString())
        mFirebaseAnalytics?.logEvent("time_open_app_ev", bundle)

        PrefAssist.setLong(PrefComm.LAST_MINUTES_OPENED_APP, CommApplication.startupTimeInSeconds / 60)
        PrefAssist.setInt(PrefComm.LAST_HOURS_OPENED_APP, LocalTime.now().hourOfDay)
    }

    // ================================================================================================
    // ADVERTISING EVENTS

    fun logLoadAd(
        type: Int,
        action: Int,
        beginLoadAdSeconds: Long,
        admobErrorCode: Int? = null,
    ) {
        if (!isAnalyticsEnabled || !isAnalyticsAdsEnabled) return
        // / <param name="type">1-banner, 2-interstitial, 3-video, 4-native, 5-open_app</param>
        // / <param name="action">1-load, 2-reload, 3-loaded, 4-load_failed</param>

        when (type) {
            AD_BANNER -> if (!isAnalyticsAdsBannerEnabled) return
            AD_INTERSTITIAL -> if (!isAnalyticsAdsInterEnabled) return
            AD_VIDEO -> if (!isAnalyticsAdsVideoEnabled) return
            AD_NATIVE -> if (!isAnalyticsAdsNativeEnabled) return
            AD_OPEN_AD -> if (!isAnalyticsAdsOpenAdEnabled) return
        }

        if (MixedUtils.currentTimeSeconds() - beginLoadAdSeconds > CommFigs.SECONDS_DAY) return

        val bundle = Bundle()
        bundle.putString("load_ad_ev_type", type.toString())
        bundle.putString("load_ad_ev_action", action.toString())
        if (admobErrorCode != null) bundle.putString("load_ad_ev_admob_error_code", admobErrorCode.toString())
//        DebugLogCustom.logd(" ${MixedUtils.currentTimeSeconds()}")
//        DebugLogCustom.logd(" ${beginLoadAdSeconds}")
//        DebugLogCustom.logd(type.toString() + " " + action.toString() + " ${MixedUtils.currentTimeSeconds() - beginLoadAdSeconds}")
        bundle.putLong("time_request_ad_ev", MixedUtils.currentTimeSeconds() - beginLoadAdSeconds)
        if (action == AD_LOADED)
            bundle.putString("time_loaded_ad_ev", (MixedUtils.currentTimeSeconds() - beginLoadAdSeconds).toString())
        when (CommFigs.WEB_CONNECTION_STATE) {
            ConnectionState.DISCONNECTED -> bundle.putString("internet_connection", "disconnected")
            ConnectionState.CONNECTED -> bundle.putString("internet_connection", "connected")
            ConnectionState.SLOW -> bundle.putString("internet_connection", "slow")
        }
        mFirebaseAnalytics?.logEvent("load_ad_ev", bundle)
    }

    fun logLoadSpecificAd(
        adTag: String,
        type: Int,
        action: Int,
        beginLoadAdSeconds: Long,
        admobErrorCode: Int? = null,
    ) {
        if (!isAnalyticsEnabled || !isAnalyticsAdsEnabled) return
        // / <param name="type">1-banner, 2-interstitial, 3-video, 4-native, 5-open_app</param>
        // / <param name="action">1-load, 2-reload, 3-loaded, 4-load_failed</param>

        when (type) {
            AD_BANNER -> if (!isAnalyticsAdsBannerEnabled) return
            AD_INTERSTITIAL -> if (!isAnalyticsAdsInterEnabled) return
            AD_VIDEO -> if (!isAnalyticsAdsVideoEnabled) return
            AD_NATIVE -> if (!isAnalyticsAdsNativeEnabled) return
            AD_OPEN_AD -> if (!isAnalyticsAdsOpenAdEnabled) return
        }

        if (MixedUtils.currentTimeSeconds() - beginLoadAdSeconds > CommFigs.SECONDS_DAY) return

        val bundle = Bundle()
        bundle.putString("load_ad_ev_type", type.toString())
        bundle.putString("load_ad_ev_action", action.toString())
        if (admobErrorCode != null) bundle.putString("load_ad_ev_admob_error_code", admobErrorCode.toString())
        bundle.putLong("time_request_ad_ev", MixedUtils.currentTimeSeconds() - beginLoadAdSeconds)
        if (action == AD_LOADED)
            bundle.putString("time_loaded_ad_ev", (MixedUtils.currentTimeSeconds() - beginLoadAdSeconds).toString())
        when (CommFigs.WEB_CONNECTION_STATE) {
            ConnectionState.DISCONNECTED -> bundle.putString("internet_connection", "disconnected")
            ConnectionState.CONNECTED -> bundle.putString("internet_connection", "connected")
            ConnectionState.SLOW -> bundle.putString("internet_connection", "slow")
        }
        mFirebaseAnalytics?.logEvent("load_ad_ev_$adTag", bundle)
    }

    fun logShowAd(
        type: Int,
        action: Int,
        admobErrorCode: Int? = null,
    ) {
        if (!isAnalyticsEnabled || !isAnalyticsAdsEnabled) return
        // / <param name="type">1-banner, 2-interstitial, 3-video, 4-native, 5-open_app</param>
        // / <param name="action">1-AD_SHOW_FAILED, 2-AD_SHOW_SUCCESS</param>

        when (type) {
            AD_BANNER -> if (!isAnalyticsAdsBannerEnabled) return
            AD_INTERSTITIAL -> if (!isAnalyticsAdsInterEnabled) return
            AD_VIDEO -> if (!isAnalyticsAdsVideoEnabled) return
            AD_NATIVE -> if (!isAnalyticsAdsNativeEnabled) return
            AD_OPEN_AD -> if (!isAnalyticsAdsOpenAdEnabled) return
        }

        val bundle = Bundle()
        bundle.putString("show_ad_ev_type", type.toString())
        bundle.putString("show_ad_ev_action", action.toString())
        if (admobErrorCode != null) bundle.putString("show_ad_ev_admob_error_code", admobErrorCode.toString())
        when (CommFigs.WEB_CONNECTION_STATE) {
            ConnectionState.DISCONNECTED -> bundle.putString("internet_connection", "disconnected")
            ConnectionState.CONNECTED -> bundle.putString("internet_connection", "connected")
            ConnectionState.SLOW -> bundle.putString("internet_connection", "slow")
        }
        mFirebaseAnalytics?.logEvent("show_ad_ev", bundle)
    }

    fun logShowSpecificAd(
        adTag: String,
        type: Int,
        action: Int,
        admobErrorCode: Int? = null,
    ) {
        if (!isAnalyticsEnabled || !isAnalyticsAdsEnabled) return
        // / <param name="type">1-banner, 2-interstitial, 3-video, 4-native, 5-open_app</param>
        // / <param name="action">1-AD_SHOW_FAILED, 2-AD_SHOW_SUCCESS</param>

        when (type) {
            AD_BANNER -> if (!isAnalyticsAdsBannerEnabled) return
            AD_INTERSTITIAL -> if (!isAnalyticsAdsInterEnabled) return
            AD_VIDEO -> if (!isAnalyticsAdsVideoEnabled) return
            AD_NATIVE -> if (!isAnalyticsAdsNativeEnabled) return
            AD_OPEN_AD -> if (!isAnalyticsAdsOpenAdEnabled) return
        }

        val bundle = Bundle()
        bundle.putString("show_ad_ev_type", type.toString())
        bundle.putString("show_ad_ev_action", action.toString())
        if (admobErrorCode != null) bundle.putString("show_ad_ev_admob_error_code", admobErrorCode.toString())
        when (CommFigs.WEB_CONNECTION_STATE) {
            ConnectionState.DISCONNECTED -> bundle.putString("internet_connection", "disconnected")
            ConnectionState.CONNECTED -> bundle.putString("internet_connection", "connected")
            ConnectionState.SLOW -> bundle.putString("internet_connection", "slow")
        }
        mFirebaseAnalytics?.logEvent("show_ad_ev_$adTag", bundle)
    }

    fun logClickAd(
        type: Int,
        beginLoadAdSeconds: Long,
    ) {
        if (!isAnalyticsEnabled || !isAnalyticsAdsEnabled) return
        // / <param name="type">1-banner, 2-interstitial, 3-video, 4-native, 5-open_app</param>
        // / <param name="action">1-load, 2-reload, 3-loaded, 4-load_failed</param>

        when (type) {
            AD_BANNER -> if (!isAnalyticsAdsBannerEnabled) return
            AD_INTERSTITIAL -> if (!isAnalyticsAdsInterEnabled) return
            AD_VIDEO -> if (!isAnalyticsAdsVideoEnabled) return
            AD_NATIVE -> if (!isAnalyticsAdsNativeEnabled) return
            AD_OPEN_AD -> if (!isAnalyticsAdsOpenAdEnabled) return
        }

        if (MixedUtils.currentTimeSeconds() - beginLoadAdSeconds > CommFigs.SECONDS_DAY) return

        val bundle = Bundle()
        bundle.putString("click_ad_ev_type", type.toString())
        bundle.putLong("time_request_ad_ev", MixedUtils.currentTimeSeconds() - beginLoadAdSeconds)
        when (CommFigs.WEB_CONNECTION_STATE) {
            ConnectionState.DISCONNECTED -> bundle.putString("internet_connection", "disconnected")
            ConnectionState.CONNECTED -> bundle.putString("internet_connection", "connected")
            ConnectionState.SLOW -> bundle.putString("internet_connection", "slow")
        }
        mFirebaseAnalytics?.logEvent("click_ad_ev", bundle)
    }

    fun logClickSpecificAd(
        adTag: String,
        type: Int,
        beginLoadAdSeconds: Long,
    ) {
        if (!isAnalyticsEnabled || !isAnalyticsAdsEnabled) return
        // / <param name="type">1-banner, 2-interstitial, 3-video, 4-native, 5-open_app</param>
        // / <param name="action">1-load, 2-reload, 3-loaded, 4-load_failed</param>

        when (type) {
            AD_BANNER -> if (!isAnalyticsAdsBannerEnabled) return
            AD_INTERSTITIAL -> if (!isAnalyticsAdsInterEnabled) return
            AD_VIDEO -> if (!isAnalyticsAdsVideoEnabled) return
            AD_NATIVE -> if (!isAnalyticsAdsNativeEnabled) return
            AD_OPEN_AD -> if (!isAnalyticsAdsOpenAdEnabled) return
        }

        if (MixedUtils.currentTimeSeconds() - beginLoadAdSeconds > CommFigs.SECONDS_DAY) return

        val bundle = Bundle()
        bundle.putString("click_ad_ev_type", type.toString())
        bundle.putLong("time_request_ad_ev", MixedUtils.currentTimeSeconds() - beginLoadAdSeconds)
        when (CommFigs.WEB_CONNECTION_STATE) {
            ConnectionState.DISCONNECTED -> bundle.putString("internet_connection", "disconnected")
            ConnectionState.CONNECTED -> bundle.putString("internet_connection", "connected")
            ConnectionState.SLOW -> bundle.putString("internet_connection", "slow")
        }
        mFirebaseAnalytics?.logEvent("click_ad_ev_$adTag", bundle)
    }

    // ================================================================================================
    // PERMISSION USER PROPERTIES
    fun collectPropertyPermissionNotification() {
        if (!isAnalyticsEnabled || !isUserPropertiesEnabled) return
        mFirebaseAnalytics?.setUserProperty(
            "perm_notification",
            if (PermissionUtils.checkNotificationPermission())
                "allow_1"
            else
                "allow_0",
        )
    }

    fun collectPropertyPermissionFullScreenIntent() {
        if (!isAnalyticsEnabled || !isUserPropertiesEnabled) return
        mFirebaseAnalytics?.setUserProperty(
            "perm_full_screen_intent",
            if (PermissionUtils.checkFullScreenIntentPermission())
                "allow_1"
            else
                "allow_0",
        )
    }

    fun collectPropertyPermissionLocation() {
        if (!isAnalyticsEnabled || !isUserPropertiesEnabled) return
        mFirebaseAnalytics?.setUserProperty(
            "perm_location",
            if (PermissionUtils.checkLocationPermission())
                "allow_1"
            else
                "allow_0",
        )
    }

    fun collectPropertyPermissionCamera() {
        if (!isAnalyticsEnabled || !isUserPropertiesEnabled) return
        mFirebaseAnalytics?.setUserProperty(
            "perm_camera",
            if (PermissionUtils.checkCameraPermission())
                "allow_1"
            else
                "allow_0",
        )
    }

    // ================================================================================================
    // OTHER USER PROPERTIES
    fun collectPropertyWidgetSuggestion(widgetName: String) {
        if (!isAnalyticsEnabled || !isUserPropertiesEnabled) return
        mFirebaseAnalytics?.setUserProperty("widget_accept", widgetName)
    }

    fun collectPropertyOpenAppInDayN() {
        if (!isAnalyticsEnabled || !isUserPropertiesEnabled) return
//        val bundle = Bundle()
//        bundle.putString("day_n", dayN)
//        mFirebaseAnalytics?.logEvent("user_open_app_in_day_n", bundle)

        var dayN = System.currentTimeMillis() - installDateInMilliseconds()
        dayN = floor(dayN.toDouble() / CommFigs.MILLIS_DAY).toLong()
        mFirebaseAnalytics?.setUserProperty("user_open_app_in_day_n", "day_$dayN")
    }

    fun collectPropertyOpenRateMeDialogCounter() {
        if (!isAnalyticsEnabled || !isUserPropertiesEnabled) return
        val key = "ANALYTICS_RATE_ME_OPEN_NUM"
        val num = PrefAssist.getInt(key, 0)
        PrefAssist.setInt(key, num + 1)
        mFirebaseAnalytics?.setUserProperty("rate_me_open_num", "num_${num + 1}")
    }

    // ================================================================================================

    fun logAdvertsDuration(params: Bundle?) {
        if (!isAnalyticsPaidAdImpressionEnabled) return
        mFirebaseAnalytics?.logEvent("ad_duration", params)
    }

    fun logPaidAdvertsClicked(params: Bundle?) {
        if (!isAnalyticsPaidAdImpressionEnabled) return
        mFirebaseAnalytics?.logEvent("paid_ad_impression", params)
    }

    // ================================================================================================
    // APP EXIT TRACKING EVENTS

    /**
     * Log event khi user nhấn nút Home để tắt app
     */
    fun logHomeButtonPressed(screenName: String) {
        if (!isAnalyticsEnabled) return
        mFirebaseAnalytics?.logEvent(
            "ev_home_button_pressed",
            Bundle().apply {
                putString("screen_name", screenName)
                putString("local_hour", LocalTime.now().hourOfDay.toString() + "h")
            }
        )
    }

    /**
     * Log event khi user nhấn nút Recent để tắt app
     */
    fun logRecentButtonPressed(screenName: String) {
        if (!isAnalyticsEnabled) return
        mFirebaseAnalytics?.logEvent(
            "ev_recent_button_pressed",
            Bundle().apply {
                putString("screen_name", screenName)
                putString("local_hour", LocalTime.now().hourOfDay.toString() + "h")
            }
        )
    }

    // ================================================================================================

    fun logPaidAdvertsX5Clicked(params: Bundle?) {
        if (!isAnalyticsPaidAdImpressionEnabled) return
        mFirebaseAnalytics?.logEvent("ad_rev_imp_x5", params)
    }

    private fun installDateInMilliseconds(): Long {
        val context = CommApplication.appContext
        val installDate: Long =
            try {
                context.packageManager.getPackageInfo(context.packageName, 0).firstInstallTime
            } catch (e: PackageManager.NameNotFoundException) {
                Calendar.getInstance().timeInMillis
            }
        return installDate
    }

    fun logCrashlyticsDeviceInformation() {
        try {
            val isEmulator: Boolean =
                (
                    (
                        Build.FINGERPRINT.startsWith("google/sdk_gphone_") &&
                            Build.FINGERPRINT.endsWith(":user/release-keys") &&
                            Build.MANUFACTURER.equals(
                                "Google",
                                ignoreCase = true,
                            ) &&
                            Build.PRODUCT.startsWith("sdk_gphone_") &&
                            Build.BRAND.equals("google", ignoreCase = true) &&
                            Build.MODEL.startsWith("sdk_gphone_") ||
                            //
                            Build.FINGERPRINT.startsWith("generic") ||
                            Build.FINGERPRINT.startsWith("unknown") ||
                            Build.MODEL.contains("google_sdk") ||
                            Build.MODEL.contains("Emulator") ||
                            Build.MODEL.contains(
                                "Android SDK built for x86",
                            ) ||
                            "QC_Reference_Phone".equals(
                                Build.BOARD,
                                ignoreCase = true,
                            ) &&
                            !"Xiaomi".equals(Build.MANUFACTURER, ignoreCase = true)
                    ) ||
                        // bluestacks
                        Build.MANUFACTURER.contains("Genymotion") ||
                        Build.HOST.equals(
                            "Build2",
                            ignoreCase = true,
                        ) ||
                        Build.BRAND.startsWith("generic") &&
                        Build.DEVICE.startsWith("generic")
                ) ||
                    Build.PRODUCT.equals("google_sdk", ignoreCase = true)

            @Suppress("DEPRECATION")
            FirebaseCrashlytics.getInstance().apply {
                log("IsEmulator $isEmulator")
                log("Language " + Locale.getDefault().displayLanguage)
                log("Country " + Locale.getDefault().displayName)
                log("CPU_ABI : " + Build.CPU_ABI)
                log("CPU_ABI2 : " + Build.CPU_ABI2)
                log("OS.ARCH : " + System.getProperty("os.arch"))
                log("SUPPORTED_ABIS : " + Build.SUPPORTED_ABIS.contentToString())
                log("SUPPORTED_32_BIT_ABIS : " + Build.SUPPORTED_32_BIT_ABIS.contentToString())
                log("SUPPORTED_64_BIT_ABIS : " + Build.SUPPORTED_64_BIT_ABIS.contentToString())
                log("StartMilliseconds : " + System.currentTimeMillis().toString())
            }
        } catch (ignored: Exception) {
        }
    }
}
